#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Resolves AssemblyInfo.cs merge conflicts between PROD and UAT branches
.DESCRIPTION
    This script helps resolve version conflicts in AssemblyInfo.cs by allowing you to choose
    which version to keep or specify a new version.
.PARAMETER TargetBranch
    The branch you're merging into (PROD or UAT)
.PARAMETER SourceBranch
    The branch you're merging from
.PARAMETER NewVersion
    Optional: Specify a new version to use instead of existing ones
#>

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("PROD", "UAT", "DEV")]
    [string]$TargetBranch,
    
    [Parameter(Mandatory=$true)]
    [ValidateSet("PROD", "UAT", "DEV")]
    [string]$SourceBranch,
    
    [Parameter(Mandatory=$false)]
    [string]$NewVersion
)

$assemblyInfoPath = "BIOMEWebApplication/Properties/AssemblyInfo.cs"

# Check if file exists
if (-not (Test-Path $assemblyInfoPath)) {
    Write-Error "AssemblyInfo.cs not found at $assemblyInfoPath"
    exit 1
}

# Get current versions from both branches
try {
    $targetVersion = (git show "${TargetBranch}:${assemblyInfoPath}" | Select-String 'AssemblyVersion\("([^"]+)"\)').Matches[0].Groups[1].Value
    $sourceVersion = (git show "${SourceBranch}:${assemblyInfoPath}" | Select-String 'AssemblyVersion\("([^"]+)"\)').Matches[0].Groups[1].Value
    
    Write-Host "Version conflict detected:" -ForegroundColor Yellow
    Write-Host "  $TargetBranch branch version: $targetVersion" -ForegroundColor Cyan
    Write-Host "  $SourceBranch branch version: $sourceVersion" -ForegroundColor Cyan
} catch {
    Write-Error "Could not read versions from branches: $_"
    exit 1
}

# Determine the version to use
$versionToUse = ""

if ($NewVersion) {
    $versionToUse = $NewVersion
    Write-Host "Using specified version: $versionToUse" -ForegroundColor Green
} else {
    # Auto-resolve based on branch logic
    if ($TargetBranch -eq "PROD") {
        # When merging to PROD, use 3-part versioning
        if ($sourceVersion -match "^(\d+\.\d+\.\d+)") {
            $versionToUse = $matches[1]
        } else {
            $versionToUse = $targetVersion
        }
        Write-Host "Resolving for PROD: Using 3-part version $versionToUse" -ForegroundColor Green
    } elseif ($TargetBranch -eq "UAT") {
        # When merging to UAT, use 4-part versioning
        if ($sourceVersion -match "^(\d+\.\d+\.\d+\.\d+)$") {
            $versionToUse = $sourceVersion
        } elseif ($targetVersion -match "^(\d+\.\d+\.\d+\.\d+)$") {
            $versionToUse = $targetVersion
        } else {
            # Convert 3-part to 4-part by adding .1
            $versionToUse = "$sourceVersion.1"
        }
        Write-Host "Resolving for UAT: Using 4-part version $versionToUse" -ForegroundColor Green
    } else {
        # For DEV or other branches, prompt user
        Write-Host "Please choose which version to use:" -ForegroundColor Yellow
        Write-Host "1. $TargetBranch version: $targetVersion"
        Write-Host "2. $SourceBranch version: $sourceVersion"
        Write-Host "3. Enter custom version"
        
        $choice = Read-Host "Enter choice (1-3)"
        switch ($choice) {
            "1" { $versionToUse = $targetVersion }
            "2" { $versionToUse = $sourceVersion }
            "3" { $versionToUse = Read-Host "Enter new version" }
            default { 
                Write-Error "Invalid choice"
                exit 1
            }
        }
    }
}

# Update AssemblyInfo.cs with the chosen version
try {
    $content = Get-Content $assemblyInfoPath
    $content = $content -replace '(?<=AssemblyVersion\(")[^"]+', $versionToUse
    $content = $content -replace '(?<=AssemblyFileVersion\(")[^"]+', $versionToUse
    $content = $content -replace '/\*\[assembly: AssemblyVersion\("[^"]+"\)\]', "/*[assembly: AssemblyVersion(`"$versionToUse`")]"
    $content = $content -replace '/\*\[assembly: AssemblyFileVersion\("[^"]+"\)\]', "[assembly: AssemblyFileVersion(`"$versionToUse`")]"
    
    Set-Content -Path $assemblyInfoPath -Value $content
    
    Write-Host "Successfully updated AssemblyInfo.cs with version: $versionToUse" -ForegroundColor Green
    
    # Stage the resolved file
    git add $assemblyInfoPath
    Write-Host "Staged resolved AssemblyInfo.cs for commit" -ForegroundColor Green
    
} catch {
    Write-Error "Failed to update AssemblyInfo.cs: $_"
    exit 1
}

Write-Host "Conflict resolution complete!" -ForegroundColor Green
Write-Host "You can now continue with your merge/rebase operation." -ForegroundColor Cyan
